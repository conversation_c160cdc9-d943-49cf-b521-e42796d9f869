package com.cleevio.fundedmind.application.module.gamepayoutsettings.exception

import com.cleevio.fundedmind.infrastructure.exception.ExtendedErrorReasonType
import com.cleevio.fundedmind.infrastructure.exception.FundedmindApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(HttpStatus.NOT_FOUND)
class GamePayoutSettingsNotFoundException(message: String) : FundedmindApiException(
    reason = ExtendedErrorReasonType.GAME_PAYOUT_SETTINGS_NOT_FOUND,
    message = message,
)
