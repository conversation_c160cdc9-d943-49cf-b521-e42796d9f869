package com.cleevio.fundedmind.application.module.gamepayoutsettings

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.gamepayoutsettings.finder.GamePayoutSettingsFinderService
import com.cleevio.fundedmind.application.module.gamepayoutsettings.query.GetGamePayoutSettingsQuery
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class GetGamePayoutSettingsQueryHandler(
    private val gamePayoutSettingsFinderService: GamePayoutSettingsFinderService,
) : QueryHandler<GetGamePayoutSettingsQuery.Result, GetGamePayoutSettingsQuery> {

    override val query = GetGamePayoutSettingsQuery::class

    @Transactional(readOnly = true)
    override fun handle(query: GetGamePayoutSettingsQuery): GetGamePayoutSettingsQuery.Result =
        gamePayoutSettingsFinderService
            .getByYear(query.year)
            .let {
                GetGamePayoutSettingsQuery.Result(
                    id = it.id,
                    year = it.year.value,
                    payoutOffset = it.payoutOffset,
                )
            }
}
