package com.cleevio.fundedmind.application.module.gamepayoutsettings

import com.cleevio.fundedmind.application.module.gamepayoutsettings.finder.GamePayoutSettingsFinderService
import com.cleevio.fundedmind.domain.gamepayoutsettings.CreateGamePayoutSettingsService
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.annotation.Profile
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Year

@Component
@Profile("!test")
class GamePayoutSettingsRegistration(
    private val createGamePayoutSettingsService: CreateGamePayoutSettingsService,
    private val gamePayoutSettingsFinderService: GamePayoutSettingsFinderService,
) {
    private val logger = logger()

    @Async
    @EventListener(ApplicationReadyEvent::class)
    fun onApplicationReady() {
        runCatching {
            logger.debug("Verifying presence of GamePayoutSettings entries...")

            val currentYear = Year.now()
            val nextYear = currentYear.plusYears(1)

            verifyYearEntry(currentYear)
            verifyYearEntry(nextYear)

            logger.debug("GamePayoutSettings entries verification completed.")
        }.onFailure {
            logger.error("Failed to verify GamePayoutSettings entries, but continuing application startup: $it", it)
        }
    }

    private fun verifyYearEntry(year: Year) {
        val existingEntry = gamePayoutSettingsFinderService.findByYear(year)

        if (existingEntry != null) {
            logger.debug("GamePayoutSettings for year: '$year' has offset: '${existingEntry.payoutOffset}'")
            return
        }

        logger.debug("Creating GamePayoutSettings for year: '$year' with goal: '1 000 000' and offset: '0'")
        createGamePayoutSettingsService.create(
            year = year,
            payoutGoal = 1_000_000.toBigDecimal(),
            offset = BigDecimal.ZERO,
        )
    }
}
