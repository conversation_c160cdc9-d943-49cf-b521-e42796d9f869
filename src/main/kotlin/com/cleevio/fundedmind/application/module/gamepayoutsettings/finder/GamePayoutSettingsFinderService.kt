package com.cleevio.fundedmind.application.module.gamepayoutsettings.finder

import com.cleevio.fundedmind.application.common.finder.BaseFinderService
import com.cleevio.fundedmind.application.module.gamepayoutsettings.exception.GamePayoutSettingsNotFoundException
import com.cleevio.fundedmind.domain.gamepayoutsettings.GamePayoutSettings
import com.cleevio.fundedmind.domain.gamepayoutsettings.GamePayoutSettingsRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Year

@Service
@Transactional(readOnly = true)
class GamePayoutSettingsFinderService(
    private val gamePayoutSettingsRepository: GamePayoutSettingsRepository,
) : BaseFinderService<GamePayoutSettings>(gamePayoutSettingsRepository) {

    override fun errorBlock(message: String) = throw GamePayoutSettingsNotFoundException(message)

    override fun getEntityType() = GamePayoutSettings::class

    fun findByYear(year: Year): GamePayoutSettings? = gamePayoutSettingsRepository.findByYear(year)

    fun getByYear(year: Year): GamePayoutSettings = findByYear(year)
        ?: throw GamePayoutSettingsNotFoundException("Game payout offset for year: '$year' not found.")
}
