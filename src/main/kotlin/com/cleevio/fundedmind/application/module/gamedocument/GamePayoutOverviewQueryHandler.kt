package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.gamedocument.port.out.GamePayoutOverviewPort
import com.cleevio.fundedmind.application.module.gamedocument.port.out.LatestStudentPayoutsPort
import com.cleevio.fundedmind.application.module.gamedocument.query.GamePayoutOverviewQuery
import org.springframework.stereotype.Component

@Component
class GamePayoutOverviewQueryHandler(
    private val gamePayoutOverviewPort: GamePayoutOverviewPort,
    private val latestStudentPayoutsPort: LatestStudentPayoutsPort,
) : QueryHandler<GamePayoutOverviewQuery.Result, GamePayoutOverviewQuery> {

    override val query = GamePayoutOverviewQuery::class

    override fun handle(query: GamePayoutOverviewQuery): GamePayoutOverviewQuery.Result {
        val payoutOverview = gamePayoutOverviewPort.getPayoutOverview(year = query.year)
        val latestStudentPayouts = latestStudentPayoutsPort.getLatestStudentPayouts(year = query.year, limit = 5)

        return GamePayoutOverviewQuery.Result(
            currentTotalPayout = payoutOverview.currentTotalPayout,
            currentPayoutGoal = payoutOverview.currentPayoutGoal,
            year = payoutOverview.year,
            latestStudentPayouts = latestStudentPayouts,
        )
    }
}