package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.gamedocument.port.out.AdminGetsPayoutOverviewPort
import com.cleevio.fundedmind.application.module.gamedocument.query.AdminGetsPayoutOverviewQuery
import org.springframework.stereotype.Component

@Component
class AdminGetsPayoutOverviewQueryHandler(
    private val adminGetsPayoutOverviewPort: AdminGetsPayoutOverviewPort,
) : QueryHandler<AdminGetsPayoutOverviewQuery.Result, AdminGetsPayoutOverviewQuery> {

    override val query = AdminGetsPayoutOverviewQuery::class

    override fun handle(query: AdminGetsPayoutOverviewQuery): AdminGetsPayoutOverviewQuery.Result =
        adminGetsPayoutOverviewPort.getByYear(query.year)
}
