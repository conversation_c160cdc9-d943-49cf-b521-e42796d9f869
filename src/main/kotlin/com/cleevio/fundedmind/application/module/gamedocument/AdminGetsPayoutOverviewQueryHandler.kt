package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.application.common.query.QueryHandler
import com.cleevio.fundedmind.application.module.gamedocument.port.out.GetPayoutOverviewPort
import com.cleevio.fundedmind.application.module.gamedocument.query.GetPayoutOverviewQuery
import org.springframework.stereotype.Component

@Component
class GetPayoutOverviewQueryHandler(
    private val getPayoutOverviewPort: GetPayoutOverviewPort,
) : QueryHandler<GetPayoutOverviewQuery.Result, GetPayoutOverviewQuery> {

    override val query = GetPayoutOverviewQuery::class

    override fun handle(query: GetPayoutOverviewQuery): GetPayoutOverviewQuery.Result =
        getPayoutOverviewPort.getByYear(query.year)
}
