package com.cleevio.fundedmind.application.module.admin.script

import com.cleevio.fundedmind.application.common.command.CommandHandler
import com.cleevio.fundedmind.application.module.admin.script.command.MigrateStudentPropertiesToHubspotCommand
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserGameLevelUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserLocationUpdatePort
import com.cleevio.fundedmind.application.module.crm.port.out.CrmUserPrivacyUpdatePort
import com.cleevio.fundedmind.application.module.location.finder.UserLocationFinderService
import com.cleevio.fundedmind.application.module.user.appuser.finder.AppUserFinderService
import com.cleevio.fundedmind.application.module.user.student.finder.StudentFinderService
import com.cleevio.fundedmind.infrastructure.config.logger
import org.springframework.stereotype.Service

@Service
class MigrateStudentPropertiesToHubspotCommandHandler(
    private val studentFinderService: StudentFinderService,
    private val appUserFinderService: AppUserFinderService,
    private val userLocationFinderService: UserLocationFinderService,
    private val crmUserPrivacyUpdatePort: CrmUserPrivacyUpdatePort,
    private val crmUserLocationUpdatePort: CrmUserLocationUpdatePort,
    private val crmUserGameLevelUpdatePort: CrmUserGameLevelUpdatePort,
) : CommandHandler<Unit, MigrateStudentPropertiesToHubspotCommand> {

    private val logger = logger()

    override val command = MigrateStudentPropertiesToHubspotCommand::class

    override fun handle(command: MigrateStudentPropertiesToHubspotCommand) {
        logger.info("Starting student properties migration to HubSpot...")

        val students = studentFinderService.findAllNonDeleted()

        if (students.isEmpty()) {
            logger.info("No Student records found. Skipping migration.")
            return
        }

        val studentIds = students.map { it.id }.toSet()
        val appUserMap = appUserFinderService.findAllByIds(studentIds).associateBy { it.id }

        var successCount = 0
        var errorCount = 0

        for (student in students) {
            val appUser = appUserMap[student.id]

            if (appUser == null) {
                logger.warn("AppUser not found for student ID: {}", student.id)
                errorCount++
                continue
            }

            val result = runCatching {
                // Update privacy settings (networking visibility and level visibility)
                crmUserPrivacyUpdatePort.updateCrmUserPrivacySettings(
                    hubspotIdentifier = appUser.hubspotIdentifier,
                    networkingVisibility = student.networkingVisibility,
                    levelVisibility = student.levelVisibility,
                )

                // Update game level
                crmUserGameLevelUpdatePort.updateCrmUserGameLevel(
                    hubspotIdentifier = appUser.hubspotIdentifier,
                    gameLevel = student.gameLevel,
                )

                // Update real location if student has a location
                val realLocation = student.locationId?.let { locationId ->
                    runCatching {
                        userLocationFinderService.getById(locationId).formattedAddress
                    }.getOrElse {
                        logger.warn(
                            "Failed to get location for student: {} [Location ID: {}]",
                            student.id,
                            locationId,
                        )
                        null
                    }
                }

                crmUserLocationUpdatePort.updateCrmUserLocation(
                    hubspotIdentifier = appUser.hubspotIdentifier,
                    realLocation = realLocation,
                )
            }

            result.onSuccess {
                logger.debug("Updated student properties for student {}", student.id)
                successCount++
            }.onFailure { exception ->
                logger.warn(
                    "Failed to update student properties for student: {} [Hubspot: {}] - Error: {}",
                    student.id,
                    appUser.hubspotIdentifier,
                    exception.message,
                )
                errorCount++
            }
        }

        logger.info(
            "Student properties migration completed. Success: {}, Errors: {}, Total: {}",
            successCount,
            errorCount,
            students.size,
        )
    }
}
