package com.cleevio.fundedmind.domain.gamepayoutsettings

import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.DomainEntity
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.DynamicUpdate
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.Year
import java.util.UUID

@Table(name = "game_payout_settings")
@Entity
@DynamicUpdate
class GamePayoutSettings private constructor(
    id: UUID,
    val year: Year,
    val payoutGoal: BigDecimal,
    payoutOffset: BigDecimal,
) : DomainEntity(id) {

    var payoutOffset: BigDecimal = payoutOffset
        private set

    companion object {
        fun newGamePayoutSettings(
            id: UUID = UUIDv7.randomUUID(),
            year: Year,
            payoutGoal: BigDecimal,
            payoutOffset: BigDecimal,
        ) = GamePayoutSettings(
            id = id,
            year = year,
            payoutGoal = payoutGoal,
            payoutOffset = payoutOffset,
        )
    }

    fun updatePayoutOffset(newPayoutOffset: BigDecimal) {
        this.payoutOffset = newPayoutOffset
    }
}

@Repository
interface GamePayoutSettingsRepository : JpaRepository<GamePayoutSettings, UUID> {
    fun findByYear(year: Year): GamePayoutSettings?
}
