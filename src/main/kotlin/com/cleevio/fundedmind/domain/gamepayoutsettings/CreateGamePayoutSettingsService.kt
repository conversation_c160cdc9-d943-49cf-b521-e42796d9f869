package com.cleevio.fundedmind.domain.gamepayoutsettings

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.Year

@Service
class CreateGamePayoutSettingsService(
    private val gamePayoutSettingsRepository: GamePayoutSettingsRepository,
) {

    @Transactional
    fun create(
        year: Year,
        payoutGoal: BigDecimal,
        offset: BigDecimal,
    ): GamePayoutSettings = gamePayoutSettingsRepository.save(
        GamePayoutSettings.newGamePayoutSettings(
            year = year,
            payoutOffset = offset,
            payoutGoal = payoutGoal,
        ),
    )
}
