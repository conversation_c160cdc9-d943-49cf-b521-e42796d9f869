package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.common.command.ImageResult
import com.cleevio.fundedmind.application.module.gamedocument.port.out.LatestStudentPayoutsPort
import com.cleevio.fundedmind.application.module.gamedocument.query.GamePayoutOverviewQuery
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.jooq.tables.references.GAME_DOCUMENT
import com.cleevio.fundedmind.jooq.tables.references.STUDENT
import org.jooq.DSLContext
import org.springframework.stereotype.Component

@Component
class LatestStudentPayoutsJooq(
    private val dslContext: DSLContext,
) : LatestStudentPayoutsPort {

    override fun getLatestStudentPayouts(): List<GamePayoutOverviewQuery.LatestStudentPayout> {
        return dslContext
            .select(
                GAME_DOCUMENT.ID,
                GAME_DOCUMENT.PAYOUT_AMOUNT,
                GAME_DOCUMENT.APPROVED_AT,
                STUDENT.FIRST_NAME,
                STUDENT.studentProfilePicture.ID,
                STUDENT.studentProfilePicture.ORIGINAL_FILE_URL,
                STUDENT.studentProfilePicture.COMPRESSED_FILE_URL,
                STUDENT.studentProfilePicture.BLUR_HASH,
            )
            .from(GAME_DOCUMENT)
            .join(STUDENT).on(GAME_DOCUMENT.STUDENT_ID.eq(STUDENT.ID))
            .where(
                GAME_DOCUMENT.TYPE.eq(GameDocumentType.PAYOUT),
                GAME_DOCUMENT.STATE.eq(GameDocumentApprovalState.APPROVED),
                GAME_DOCUMENT.APPROVED_AT.isNotNull,
            )
            .orderBy(GAME_DOCUMENT.APPROVED_AT.desc())
            .limit(5)
            .fetch()
            .map { record ->
                GamePayoutOverviewQuery.LatestStudentPayout(
                    firstName = record[STUDENT.FIRST_NAME]!!,
                    profilePicture = record[STUDENT.studentProfilePicture.ID]?.let { imageId ->
                        ImageResult(
                            imageId = imageId,
                            imageOriginalUrl = record[STUDENT.studentProfilePicture.ORIGINAL_FILE_URL]!!,
                            imageCompressedUrl = record[STUDENT.studentProfilePicture.COMPRESSED_FILE_URL]!!,
                            imageBlurHash = record[STUDENT.studentProfilePicture.BLUR_HASH]!!,
                        )
                    },
                    approvedAt = record[GAME_DOCUMENT.APPROVED_AT]!!,
                    payoutAmount = record[GAME_DOCUMENT.PAYOUT_AMOUNT]!!,
                )
            }
    }
}