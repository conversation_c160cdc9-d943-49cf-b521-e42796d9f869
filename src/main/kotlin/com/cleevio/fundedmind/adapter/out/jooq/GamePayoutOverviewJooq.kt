package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.module.gamedocument.port.out.GamePayoutOverviewPort
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.jooq.tables.references.GAME_DOCUMENT
import com.cleevio.fundedmind.jooq.tables.references.GAME_PAYOUT_SETTINGS
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Year

@Component
class GamePayoutOverviewJooq(
    private val dslContext: DSLContext,
) : GamePayoutOverviewPort {

    override fun getPayoutOverview(year: Year): GamePayoutOverviewPort.PayoutOverview {
        val gamePayoutSettings = dslContext
            .select(
                GAME_PAYOUT_SETTINGS.PAYOUT_GOAL,
                GAME_PAYOUT_SETTINGS.PAYOUT_OFFSET,
                GAME_PAYOUT_SETTINGS.YEAR,
            )
            .from(GAME_PAYOUT_SETTINGS)
            .where(GAME_PAYOUT_SETTINGS.YEAR.eq(year.value))
            .fetchOne()

        val payoutGoal = gamePayoutSettings?.get(GAME_PAYOUT_SETTINGS.PAYOUT_GOAL) ?: BigDecimal.ZERO
        val payoutOffset = gamePayoutSettings?.get(GAME_PAYOUT_SETTINGS.PAYOUT_OFFSET) ?: BigDecimal.ZERO
        val yearValue = gamePayoutSettings?.get(GAME_PAYOUT_SETTINGS.YEAR) ?: year.value

        val totalPayout = dslContext
            .select(DSL.sum(GAME_DOCUMENT.PAYOUT_AMOUNT).`as`("total_payout"))
            .from(GAME_DOCUMENT)
            .where(
                GAME_DOCUMENT.TYPE.eq(GameDocumentType.PAYOUT),
                GAME_DOCUMENT.STATE.eq(GameDocumentApprovalState.APPROVED),
                DSL.year(GAME_DOCUMENT.PAYOUT_DATE).eq(year.value),
            )
            .fetchOne()
            ?.get("total_payout", BigDecimal::class.java) ?: BigDecimal.ZERO

        val currentTotalPayout = totalPayout.add(payoutOffset)

        return GamePayoutOverviewPort.PayoutOverview(
            currentTotalPayout = currentTotalPayout,
            currentPayoutGoal = payoutGoal,
            year = yearValue,
        )
    }
}
