package com.cleevio.fundedmind.adapter.out.jooq

import com.cleevio.fundedmind.application.module.gamedocument.port.out.GetPayoutOverviewPort
import com.cleevio.fundedmind.application.module.gamedocument.query.GetPayoutOverviewQuery
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentApprovalState
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.jooq.tables.references.GAME_DOCUMENT
import com.cleevio.fundedmind.jooq.tables.references.GAME_PAYOUT_SETTINGS
import org.jooq.DSLContext
import org.jooq.impl.DSL
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Year

@Component
class GetPayoutOverviewJooq(
    private val dslContext: DSLContext,
) : GetPayoutOverviewPort {

    override fun getByYear(year: Year): GetPayoutOverviewQuery.Result {
        val gamePayoutSettingsForYear = gamePayoutSettingsForYear(year)
        val payoutDocumentsForYear = payoutDocumentsForYear(year)

        return dslContext
            .select(
                payoutDocumentsForYear,
                gamePayoutSettingsForYear,
            )
            .from(DSL.dual())
            .fetchOne()
            ?.let { record ->
                val offsetAmount: BigDecimal = record[gamePayoutSettingsForYear]
                    ?.singleOrNull()
                    ?.map { it[GAME_PAYOUT_SETTINGS.PAYOUT_OFFSET] }
                    ?: BigDecimal.ZERO

                val payoutDocuments = record[payoutDocumentsForYear]

                val approvedPayouts = payoutDocuments.count()

                val realTotalPayout = payoutDocuments.sumOf { it[GAME_DOCUMENT.PAYOUT_AMOUNT] ?: BigDecimal.ZERO }
                val offsetTotalPayout = realTotalPayout + offsetAmount

                GetPayoutOverviewQuery.Result(
                    offsetTotalPayout = offsetTotalPayout,
                    realTotalPayout = realTotalPayout,
                    approvedPayouts = approvedPayouts,
                )
            }
            ?: GetPayoutOverviewQuery.Result(
                offsetTotalPayout = BigDecimal.ZERO,
                realTotalPayout = BigDecimal.ZERO,
                approvedPayouts = 0,
            )
    }

    private fun payoutDocumentsForYear(year: Year) = multiset(
        select(
            GAME_DOCUMENT.ID,
            GAME_DOCUMENT.PAYOUT_AMOUNT,
        )
            .from(GAME_DOCUMENT)
            .where(
                GAME_DOCUMENT.TYPE.eq(GameDocumentType.PAYOUT),
                GAME_DOCUMENT.STATE.eq(GameDocumentApprovalState.APPROVED),
                DSL.year(GAME_DOCUMENT.PAYOUT_DATE).eq(year.value),
            ),
    )

    private fun gamePayoutSettingsForYear(year: Year) = multiset(
        select(
            GAME_PAYOUT_SETTINGS.ID,
            GAME_PAYOUT_SETTINGS.PAYOUT_OFFSET,
        )
            .from(GAME_PAYOUT_SETTINGS)
            .where(GAME_PAYOUT_SETTINGS.YEAR.eq(year.value))
            .limit(1),
    )
}
