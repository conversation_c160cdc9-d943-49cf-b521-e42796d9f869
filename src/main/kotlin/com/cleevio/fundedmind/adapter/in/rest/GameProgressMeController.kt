package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.query.QueryBus
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Game Document [Admin]")
@RestController
@SwaggerBearerToken
@RequestMapping("/game/me")
class GameProgressMeController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @GetMapping("/payout-overview", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getGamePayoutOverview(): GamePayoutOverviewQuery.Result = queryBus(GamePayoutOverviewQuery())
}
