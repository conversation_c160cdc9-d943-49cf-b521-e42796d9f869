package com.cleevio.fundedmind.adapter.`in`.rest

import com.cleevio.fundedmind.adapter.`in`.ApiVersion
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollDesc
import com.cleevio.fundedmind.adapter.`in`.InfiniteScrollSlice
import com.cleevio.fundedmind.adapter.`in`.SwaggerBearerToken
import com.cleevio.fundedmind.adapter.`in`.rest.request.CreateGameDocumentRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.DenyGameDocumentRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.SearchGameDocumentRequest
import com.cleevio.fundedmind.adapter.`in`.rest.request.UpdateGameDocumentRequest
import com.cleevio.fundedmind.application.common.command.CommandBus
import com.cleevio.fundedmind.application.common.command.IdResult
import com.cleevio.fundedmind.application.common.query.QueryBus
import com.cleevio.fundedmind.application.module.gamedocument.command.ApproveGameDocumentCommand
import com.cleevio.fundedmind.application.module.gamedocument.query.GetGameDocumentDetailQuery
import com.cleevio.fundedmind.application.module.gamedocument.query.GetPayoutOverviewQuery
import com.cleevio.fundedmind.application.module.gamedocument.query.SearchGameDocumentsQuery
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import jakarta.annotation.security.RolesAllowed
import org.springdoc.core.annotations.ParameterObject
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.time.Year
import java.util.UUID

@Tag(name = "Game Document [Admin]")
@RestController
@SwaggerBearerToken
@RequestMapping("/game-documents")
class GameDocumentController(
    private val commandBus: CommandBus,
    private val queryBus: QueryBus,
) {

    @RolesAllowed(UserRole.Companion.ADMIN_ROLE)
    @GetMapping("/{gameDocumentId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getGameDocumentDetail(@PathVariable gameDocumentId: UUID): GetGameDocumentDetailQuery.Result =
        queryBus(GetGameDocumentDetailQuery(gameDocumentId = gameDocumentId))

    @Operation(
        description = """
            Admin creates new game document.
            400 - type is payout but payout amount is null
            400 - payout amount is negative
            400 - truth score is negative
        """,
    )
    @RolesAllowed(UserRole.Companion.ADMIN_ROLE)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.CREATED)
    fun createGameDocument(@RequestBody request: CreateGameDocumentRequest): IdResult = commandBus(request.toCommand())

    @Operation(
        description = """
            Admin updates a game document.
            400 - type is payout but payout amount is null
            400 - payout amount is negative
            400 - truth score is negative
        """,
    )
    @RolesAllowed(UserRole.Companion.ADMIN_ROLE)
    @PutMapping("/{gameDocumentId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun updateGameDocument(
        @PathVariable gameDocumentId: UUID,
        @RequestBody request: UpdateGameDocumentRequest,
    ) {
        commandBus(request.toCommand(gameDocumentId = gameDocumentId))
    }

    @Operation(
        description = """
            Admin approves a game document.
        """,
    )
    @RolesAllowed(UserRole.Companion.ADMIN_ROLE)
    @PostMapping("/{gameDocumentId}/approve", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun approveGameDocument(@PathVariable gameDocumentId: UUID) {
        commandBus(ApproveGameDocumentCommand(gameDocumentId = gameDocumentId))
    }

    @Operation(
        description = """
            Admin denies a game document with a message.
        """,
    )
    @RolesAllowed(UserRole.Companion.ADMIN_ROLE)
    @PostMapping("/{gameDocumentId}/deny", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.NO_CONTENT)
    fun denyGameDocument(
        @PathVariable gameDocumentId: UUID,
        @RequestBody request: DenyGameDocumentRequest,
    ) {
        commandBus(request.toCommand(gameDocumentId = gameDocumentId))
    }

    @Operation(
        description = """
            Search game documents with infinite scroll, sorted by UUID desc.
            Filterable by studentId and state.
        """,
    )
    @RolesAllowed(UserRole.Companion.ADMIN_ROLE)
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun searchGameDocuments(
        @ParameterObject infiniteScroll: InfiniteScrollDesc.Identifier,
        @RequestBody request: SearchGameDocumentRequest,
    ): InfiniteScrollSlice<SearchGameDocumentsQuery.Result, UUID> =
        queryBus(request.toQuery(infiniteScroll = infiniteScroll))

    @Operation(
        description = """
            Get payout overview for the current year.
            Returns total payout amounts and count of approved payouts.
        """,
    )
    @RolesAllowed(UserRole.Companion.ADMIN_ROLE)
    @GetMapping("/payout-overview/current", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getCurrentPayoutOverview(): GetPayoutOverviewQuery.Result = queryBus(GetPayoutOverviewQuery())

    @Operation(
        description = """
            Get payout overview for the given year.
        """,
    )
    @RolesAllowed(UserRole.Companion.ADMIN_ROLE)
    @GetMapping("/payout-overview/{year}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(HttpStatus.OK)
    fun getPayoutOverview(@PathVariable year: Int): GetPayoutOverviewQuery.Result =
        queryBus(GetPayoutOverviewQuery(year = Year.of(year)))
}
