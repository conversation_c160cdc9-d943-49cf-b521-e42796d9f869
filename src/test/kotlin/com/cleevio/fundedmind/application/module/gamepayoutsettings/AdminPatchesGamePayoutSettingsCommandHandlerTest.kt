package com.cleevio.fundedmind.application.module.gamepayoutsettings

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamepayoutsettings.command.AdminPatchesGamePayoutSettingsCommand
import com.cleevio.fundedmind.application.module.gamepayoutsettings.exception.GamePayoutSettingsNotFoundException
import com.cleevio.fundedmind.domain.gamepayoutsettings.GamePayoutSettingsRepository
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.repository.findByIdOrNull
import java.math.BigDecimal
import java.time.Year

class AdminPatchesGamePayoutSettingsCommandHandlerTest(
    @Autowired private val underTest: AdminPatchesGamePayoutSettingsCommandHandler,
    @Autowired private val gamePayoutSettingsRepository: GamePayoutSettingsRepository,
) : IntegrationTest() {

    @Test
    fun `should update existing game payout offset`() {
        val existingOffset = dataHelper.getGamePayoutSettings(
            year = Year.of(2025),
            payoutOffset = BigDecimal.ZERO,
        )

        underTest.handle(
            AdminPatchesGamePayoutSettingsCommand(
                year = Year.of(2025),
                offset = BigDecimal("10.50"),
            ),
        )

        gamePayoutSettingsRepository.findByIdOrNull(existingOffset.id)!!.run {
            payoutOffset shouldBeEqualComparingTo BigDecimal("10.50")
        }
    }

    @Test
    fun `should update existing game payout offset with negative value`() {
        val existingOffset = dataHelper.getGamePayoutSettings(
            year = Year.of(2025),
            payoutOffset = BigDecimal.ZERO,
        )

        underTest.handle(
            AdminPatchesGamePayoutSettingsCommand(
                year = Year.of(2025),
                offset = BigDecimal("-10.50"),
            ),
        )

        gamePayoutSettingsRepository.findByIdOrNull(existingOffset.id)!!.run {
            payoutOffset shouldBeEqualComparingTo BigDecimal("-10.50")
        }
    }

    @Test
    fun `should throw exception when game payout offset not found`() {
        shouldThrow<GamePayoutSettingsNotFoundException> {
            underTest.handle(
                AdminPatchesGamePayoutSettingsCommand(
                    year = Year.of(1999),
                    offset = BigDecimal("5.00"),
                ),
            )
        }
    }
}
