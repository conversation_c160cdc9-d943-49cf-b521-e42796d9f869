package com.cleevio.fundedmind.application.module.gamepayoutsettings

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamepayoutsettings.exception.GamePayoutSettingsNotFoundException
import com.cleevio.fundedmind.application.module.gamepayoutsettings.query.GetGamePayoutSettingsQuery
import com.cleevio.fundedmind.toUUID
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.Year

class GetGamePayoutSettingsQueryHandlerTest(
    @Autowired private val underTest: GetGamePayoutSettingsQueryHandler,
) : IntegrationTest() {

    @ParameterizedTest
    @CsvSource(
        delimiter = ';',
        nullValues = ["null"],
        value = [
            "2025; 300.25",
            "2025; -300.25",
        ],
    )
    fun `should return game payout offset for specified year`(
        year: Int,
        payoutOffset: BigDecimal,
    ) {
        // Given
        dataHelper.getGamePayoutSettings(
            id = 1.toUUID(),
            year = Year.of(year),
            payoutOffset = payoutOffset,
        )

        // When
        val result = underTest.handle(GetGamePayoutSettingsQuery(year = Year.of(year)))

        // Then
        result.run {
            id shouldBe 1.toUUID()
            this.year shouldBe year
            this.payoutOffset shouldBeEqualComparingTo payoutOffset
        }
    }

    @Test
    fun `should throw exception when no game payout offset exists for year`() {
        // No game payout offset created for this year
        // When/Then
        shouldThrow<GamePayoutSettingsNotFoundException> {
            underTest.handle(GetGamePayoutSettingsQuery(year = Year.of(2023)))
        }
    }
}
