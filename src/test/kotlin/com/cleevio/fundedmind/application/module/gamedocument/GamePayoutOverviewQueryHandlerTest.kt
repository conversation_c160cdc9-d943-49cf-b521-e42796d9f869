package com.cleevio.fundedmind.application.module.gamedocument

import com.cleevio.fundedmind.IntegrationTest
import com.cleevio.fundedmind.application.module.gamedocument.query.GamePayoutOverviewQuery
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.shouldBeAbout
import com.cleevio.fundedmind.toInstant
import com.cleevio.fundedmind.toUUID
import io.kotest.matchers.comparables.shouldBeEqualComparingTo
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Year

class GamePayoutOverviewQueryHandlerTest(
    @Autowired private val underTest: GamePayoutOverviewQueryHandler,
) : IntegrationTest() {

    @Test
    fun `should return payout overview with approved payouts and latest student payouts`() {
        // Create students
        val student1 = dataHelper.getAppUser(id = 1.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "John",
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = { student ->
                    student.changeProfilePicture(
                        dataHelper.getImage(
                            type = FileType.STUDENT_PROFILE_PICTURE,
                            originalFileUrl = "profile-url",
                            compressedFileUrl = "profile-url-comp",
                            blurHash = "123",
                        ).id,
                    )
                },
            )
        }
        val student2 = dataHelper.getAppUser(id = 2.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Jane", studentTier = StudentTier.EXCLUSIVE)
        }
        val student3 = dataHelper.getAppUser(id = 3.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, firstName = "Bob", studentTier = StudentTier.MASTERCLASS)
        }

        // Create approved payout documents for the year
        val approvedPayout1 = dataHelper.getGameDocument(
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("1000.00"),
            payoutDate = LocalDate.of(2025, 6, 15),
            entityModifier = { it.approve(now = "2025-06-15T10:00:00Z".toInstant()) },
        )

        val approvedPayout2 = dataHelper.getGameDocument(
            studentId = student2.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("500.50"),
            payoutDate = LocalDate.of(2025, 8, 20),
            entityModifier = { it.approve(now =  "2025-08-20T10:00:00Z".toInstant()) },
        )

        val approvedPayout3 = dataHelper.getGameDocument(
            studentId = student3.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("750.25"),
            payoutDate = LocalDate.of(2025, 9, 5),
            entityModifier = { it.approve(now =  "2025-09-05T10:00:00Z".toInstant()) },
        )

        // Create a waiting payout (should not be counted)
        val waitingPayout = dataHelper.getGameDocument(
            studentId = student1.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("300.00"),
            payoutDate = LocalDate.of(2025, 9, 10),
        ) // state remains WAITING

        // Create a denied payout (should not be counted)
        val deniedPayout = dataHelper.getGameDocument(
            studentId = student2.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("400.00"),
            payoutDate = LocalDate.of(2025, 9, 15),
            entityModifier = { it.deny("Rejected") },
        )

        // Create payout settings for the year
        dataHelper.getGamePayoutSettings(
            year = year,
            payoutGoal = BigDecimal("5000.00"),
            payoutOffset = BigDecimal("250.75"),
        )

        val result = underTest.handle(GamePayoutOverviewQuery(year = year))

        result.run {
            currentTotalPayout shouldBeEqualComparingTo BigDecimal("2501.50") // 1000.00 + 500.50 + 750.25 + 250.75
            currentPayoutGoal shouldBeEqualComparingTo BigDecimal("5000.00")
            year shouldBe 2025
            latestStudentPayouts.size shouldBe 3

            latestStudentPayouts[0].run {
                approvedAt shouldBeAbout
                payoutAmount shouldBeEqualComparingTo BigDecimal("750.25")
                firstName shouldBe "Bob"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "profile-url"
                    imageCompressedUrl shouldBe "profile-url-comp"
                    imageBlurHash shouldBe "123"
                }
            }

            // Check that the latest payouts are ordered by approvedAt desc
            latestStudentPayouts[0].payoutAmount shouldBeEqualComparingTo BigDecimal("750.25")
            latestStudentPayouts[1].payoutAmount shouldBeEqualComparingTo BigDecimal("500.50")
            latestStudentPayouts[2].payoutAmount shouldBeEqualComparingTo BigDecimal("1000.00")

            latestStudentPayouts[0].firstName shouldBe "Bob"
            latestStudentPayouts[1].firstName shouldBe "Jane"
            latestStudentPayouts[2].firstName shouldBe "John"
        }
    }

    @Test
    fun `should return zero values when no approved payouts exist`() {
        val year = Year.of(2024)

        // Create a student
        val student = dataHelper.getAppUser(id = 304.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(id = it.id, studentTier = StudentTier.BASECAMP)
        }

        // Create only waiting/denied payouts
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("100.00"),
            payoutDate = LocalDate.of(2024, 5, 10),
        ) // state remains WAITING

        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("200.00"),
            payoutDate = LocalDate.of(2024, 6, 15),
            entityModifier = { it.deny("Rejected") },
        )

        // Create payout settings for the year
        dataHelper.getGamePayoutSettings(
            year = year,
            payoutGoal = BigDecimal("3000.00"),
            payoutOffset = BigDecimal("100.00"),
        )

        val result = underTest.handle(GamePayoutOverviewQuery(year = year))

        result.run {
            currentTotalPayout shouldBeEqualComparingTo BigDecimal("100.00") // 0 + 100.00
            currentPayoutGoal shouldBeEqualComparingTo BigDecimal("3000.00")
            year shouldBe 2024
            latestStudentPayouts.size shouldBe 0
        }
    }

    @Test
    fun `should use current year as default when no year specified`() {
        val currentYear = Year.of(2025)

        // Create a student
        val student = dataHelper.getAppUser(id = 305.toUUID(), userRole = UserRole.STUDENT).also {
            dataHelper.getStudent(
                id = it.id,
                firstName = "Alice",
                studentTier = StudentTier.MASTERCLASS,
                entityModifier = { student ->
                    student.changeProfilePicture(
                        dataHelper.getImage(
                            type = FileType.STUDENT_PROFILE_PICTURE,
                            originalFileUrl = "profile-url",
                            compressedFileUrl = "profile-url-comp",
                            blurHash = "123",
                        ).id,
                    )
                },
            )
        }

        // Create approved payout for current year
        dataHelper.getGameDocument(
            studentId = student.id,
            type = GameDocumentType.PAYOUT,
            payoutAmount = BigDecimal("123.45"),
            payoutDate = LocalDate.now(),
            entityModifier = { it.approve() },
        )

        // Create payout settings for current year
        dataHelper.getGamePayoutSettings(
            year = currentYear,
            payoutGoal = BigDecimal("2000.00"),
            payoutOffset = BigDecimal("10.55"),
        )

        val result = underTest.handle(GamePayoutOverviewQuery()) // No year specified, should use current year

        result.run {
            currentTotalPayout shouldBeEqualComparingTo BigDecimal("134.00") // 123.45 + 10.55
            currentPayoutGoal shouldBeEqualComparingTo BigDecimal("2000.00")
            year shouldBe 2025
            latestStudentPayouts.size shouldBe 1
            latestStudentPayouts.single().run {
                firstName shouldBe "Alice"
                profilePicture shouldNotBe null
                profilePicture!!.run {
                    imageOriginalUrl shouldBe "profile-url"
                    imageCompressedUrl shouldBe "profile-url-comp"
                    imageBlurHash shouldBe "123"
                }
                approvedAt shouldBe "2025-07-10T10:00:00.000Z".toInstant()
                payoutAmount shouldBeEqualComparingTo BigDecimal("123.45")

            }
        }
    }
}
