package com.cleevio.fundedmind

import com.cleevio.fundedmind.application.common.type.CalendlyEventUri
import com.cleevio.fundedmind.application.common.type.CalendlyUserUri
import com.cleevio.fundedmind.application.common.type.FirebaseId
import com.cleevio.fundedmind.application.common.type.StripeCustomerId
import com.cleevio.fundedmind.application.common.type.StripeProductId
import com.cleevio.fundedmind.application.common.type.StripeSessionId
import com.cleevio.fundedmind.application.common.util.UUIDv7
import com.cleevio.fundedmind.domain.comment.Comment
import com.cleevio.fundedmind.domain.comment.CommentLike
import com.cleevio.fundedmind.domain.comment.CommentLikeRepository
import com.cleevio.fundedmind.domain.comment.CommentRepository
import com.cleevio.fundedmind.domain.comment.ThreadCommentNotification
import com.cleevio.fundedmind.domain.comment.ThreadCommentNotificationRepository
import com.cleevio.fundedmind.domain.common.AppButton
import com.cleevio.fundedmind.domain.common.AppButtonWithLink
import com.cleevio.fundedmind.domain.common.constant.BadgeColor
import com.cleevio.fundedmind.domain.common.constant.Color
import com.cleevio.fundedmind.domain.common.constant.Country
import com.cleevio.fundedmind.domain.common.constant.CourseCategory
import com.cleevio.fundedmind.domain.common.constant.GameLevel
import com.cleevio.fundedmind.domain.common.constant.LevelVisibility
import com.cleevio.fundedmind.domain.common.constant.NetworkingVisibility
import com.cleevio.fundedmind.domain.common.constant.StudentTier
import com.cleevio.fundedmind.domain.common.constant.TraderMentoringAvailability
import com.cleevio.fundedmind.domain.course.Course
import com.cleevio.fundedmind.domain.course.CourseRepository
import com.cleevio.fundedmind.domain.coursemodule.CourseModule
import com.cleevio.fundedmind.domain.coursemodule.CourseModuleRepository
import com.cleevio.fundedmind.domain.file.AppFile
import com.cleevio.fundedmind.domain.file.AppFileRepository
import com.cleevio.fundedmind.domain.file.constant.FileType
import com.cleevio.fundedmind.domain.gamedocument.GameDocument
import com.cleevio.fundedmind.domain.gamedocument.GameDocumentRepository
import com.cleevio.fundedmind.domain.gamedocument.constant.GameDocumentType
import com.cleevio.fundedmind.domain.gamedocument.constant.IssuingCompany
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelReward
import com.cleevio.fundedmind.domain.gamelevelreward.GameLevelRewardRepository
import com.cleevio.fundedmind.domain.gamelevelreward.constant.GameLevelRewardType
import com.cleevio.fundedmind.domain.gamepayoutsettings.GamePayoutSettings
import com.cleevio.fundedmind.domain.gamepayoutsettings.GamePayoutSettingsRepository
import com.cleevio.fundedmind.domain.highlight.Highlight
import com.cleevio.fundedmind.domain.highlight.HighlightRepository
import com.cleevio.fundedmind.domain.lesson.Lesson
import com.cleevio.fundedmind.domain.lesson.LessonRepository
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachment
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentRepository
import com.cleevio.fundedmind.domain.lesson.attachment.LessonAttachmentValue
import com.cleevio.fundedmind.domain.lesson.constant.LessonAttachmentType
import com.cleevio.fundedmind.domain.location.UserLocation
import com.cleevio.fundedmind.domain.location.UserLocationRepository
import com.cleevio.fundedmind.domain.meeting.Meeting
import com.cleevio.fundedmind.domain.meeting.MeetingRepository
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.TraderInMeeting
import com.cleevio.fundedmind.domain.meeting.traderinmeeting.TraderInMeetingRepository
import com.cleevio.fundedmind.domain.mentoring.Mentoring
import com.cleevio.fundedmind.domain.mentoring.MentoringRepository
import com.cleevio.fundedmind.domain.mentoringmeeting.MentoringMeeting
import com.cleevio.fundedmind.domain.mentoringmeeting.MentoringMeetingRepository
import com.cleevio.fundedmind.domain.networking.NetworkingMessage
import com.cleevio.fundedmind.domain.networking.NetworkingMessageRepository
import com.cleevio.fundedmind.domain.product.Product
import com.cleevio.fundedmind.domain.product.ProductRepository
import com.cleevio.fundedmind.domain.progress.CourseModuleProgress
import com.cleevio.fundedmind.domain.progress.CourseModuleProgressRepository
import com.cleevio.fundedmind.domain.progress.CourseProgress
import com.cleevio.fundedmind.domain.progress.CourseProgressRepository
import com.cleevio.fundedmind.domain.progress.LessonProgress
import com.cleevio.fundedmind.domain.progress.LessonProgressRepository
import com.cleevio.fundedmind.domain.referral.Referral
import com.cleevio.fundedmind.domain.referral.ReferralRepository
import com.cleevio.fundedmind.domain.user.Questionnaire
import com.cleevio.fundedmind.domain.user.appuser.AppUser
import com.cleevio.fundedmind.domain.user.appuser.AppUserRepository
import com.cleevio.fundedmind.domain.user.appuser.VerificationCode
import com.cleevio.fundedmind.domain.user.appuser.VerificationCodeRepository
import com.cleevio.fundedmind.domain.user.appuser.constant.UserRole
import com.cleevio.fundedmind.domain.user.onboarding.Onboarding
import com.cleevio.fundedmind.domain.user.onboarding.OnboardingRepository
import com.cleevio.fundedmind.domain.user.student.Student
import com.cleevio.fundedmind.domain.user.student.StudentDiscord
import com.cleevio.fundedmind.domain.user.student.StudentDiscordRepository
import com.cleevio.fundedmind.domain.user.student.StudentRepository
import com.cleevio.fundedmind.domain.user.toQuestionnaire
import com.cleevio.fundedmind.domain.user.trader.Trader
import com.cleevio.fundedmind.domain.user.trader.TraderRepository
import com.cleevio.fundedmind.domain.user.trader.constant.TraderTag
import com.cleevio.fundedmind.jooq.tables.references.LESSON_PROGRESS
import com.cleevio.fundedmind.jooq.tables.references.MENTORING
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.Year
import java.time.temporal.ChronoUnit
import java.util.UUID

@Service
class IntegrationDataTestHelper(
    private val timestampModifier: TimestampModifier,
    private val appUserRepository: AppUserRepository,
    private val studentRepository: StudentRepository,
    private val traderRepository: TraderRepository,
    private val appFileRepository: AppFileRepository,
    private val onboardingRepository: OnboardingRepository,
    private val productRepository: ProductRepository,
    private val meetingRepository: MeetingRepository,
    private val traderInMeetingRepository: TraderInMeetingRepository,
    private val verificationCodeRepository: VerificationCodeRepository,
    private val highlightRepository: HighlightRepository,
    private val courseRepository: CourseRepository,
    private val courseModuleRepository: CourseModuleRepository,
    private val lessonRepository: LessonRepository,
    private val lessonAttachmentRepository: LessonAttachmentRepository,
    private val lessonProgressRepository: LessonProgressRepository,
    private val commentRepository: CommentRepository,
    private val threadCommentNotificationRepository: ThreadCommentNotificationRepository,
    private val commentLikeRepository: CommentLikeRepository,
    private val courseModuleProgressRepository: CourseModuleProgressRepository,
    private val courseProgressRepository: CourseProgressRepository,
    private val mentoringRepository: MentoringRepository,
    private val referralRepository: ReferralRepository,
    private val studentDiscordRepository: StudentDiscordRepository,
    private val mentoringMeetingRepository: MentoringMeetingRepository,
    private val userLocationRepository: UserLocationRepository,
    private val networkingMessageRepository: NetworkingMessageRepository,
    private val gameLevelRewardRepository: GameLevelRewardRepository,
    private val gameDocumentRepository: GameDocumentRepository,
    private val gamePayoutSettingsRepository: GamePayoutSettingsRepository,
) {

    fun getImage(
        id: UUID = UUIDv7.randomUUID(),
        extension: String = "png",
        type: FileType,
        originalFileUrl: String = "https://example.com/${type.name}/$id-orig.$extension",
        compressedFileUrl: String = "https://example.com/${type.name}/$id-comp.$extension",
        blurHash: String = "eyJpZCI6IkxGR3RObV0tMDBUZH1AeGFvZ2JJMDBLNWs9clgiLCJ3IjoyMSwiaCI6MzIsInZlciI6MX0=",
    ): AppFile = appFileRepository.saveAndFlush(
        AppFile.newImage(
            id = id,
            originalFileUrl = originalFileUrl,
            compressedFileUrl = compressedFileUrl,
            blurHash = blurHash,
            extension = extension,
            type = type,
        ),
    )

    fun getDocument(
        id: UUID = UUIDv7.randomUUID(),
        extension: String = "pdf",
        type: FileType = FileType.LESSON_ATTACHMENT,
    ): AppFile = appFileRepository.saveAndFlush(
        AppFile.newDocument(
            id = id,
            extension = extension,
            type = type,
        ),
    )

    fun getAppUser(
        id: UUID = UUIDv7.randomUUID(),
        email: String = "user+${id.suffix}@fundedmind.test",
        firebaseIdentifier: FirebaseId = "firebase-${id.suffix}",
        stripeIdentifier: StripeCustomerId = "cus_${id.suffix}",
        userRole: UserRole = UserRole.STUDENT,
        hubspotIdentifier: Long = 1L,
        traderReferral: String? = null,
        entityModifier: (AppUser) -> Unit = {},
    ): AppUser = appUserRepository.saveAndFlush(
        AppUser.newStudentAccount(
            id = id,
            email = email,
            firebaseIdentifier = firebaseIdentifier,
            hubspotIdentifier = hubspotIdentifier,
            stripeIdentifier = stripeIdentifier,
            traderReferral = traderReferral,
        ).also { appUser ->
            appUser.changeRole(userRole)
            entityModifier(appUser)
        },
    )

    fun getOnboarding(
        id: UUID,
        entityModifier: (Onboarding) -> Unit = {},
    ): Onboarding = onboardingRepository.saveAndFlush(
        Onboarding.initiateAfterSignUp(
            userId = id,
        ).also {
            entityModifier(it)
        },
    )

    fun getStudent(
        id: UUID = UUIDv7.randomUUID(),
        profilePictureFileId: UUID? = null,
        studentTier: StudentTier = StudentTier.BASECAMP,
        tierUpgradedAt: Instant = Instant.now(),
        firstName: String = "Joe",
        lastName: String = "Doe",
        phone: String = "+123",
        biography: String? = null,
        country: Country = Country.CZ,
        questionnaire: Questionnaire = DataTestHelper.prepareOnboardingQuestionnaireInput().toQuestionnaire(),
        firstNameVocative: String = "Joe",
        lastNameVocative: String = "Doe",
        networkingVisibility: NetworkingVisibility = NetworkingVisibility.ENABLED,
        levelVisibility: LevelVisibility = LevelVisibility.ENABLED,
        locationId: UUID? = null,
        gameLevel: GameLevel = GameLevel.ONE,
        entityModifier: (Student) -> Unit = {},
    ): Student = studentRepository.saveAndFlush(
        Student.fromOnboarding(
            id = id,
            profilePictureFileId = profilePictureFileId,
            studentTier = studentTier,
            tierUpgradedAt = tierUpgradedAt,
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            biography = biography,
            country = country,
            questionnaire = questionnaire,
            firstNameVocative = firstNameVocative,
            lastNameVocative = lastNameVocative,
            locationId = locationId,
        ).also {
            it.patchPrivacy(networkingVisibility, levelVisibility)
            it.updateGameLevel(gameLevel)
            entityModifier(it)
        },
    )

    fun getStudentDiscord(
        id: UUID = UUIDv7.randomUUID(),
        studentId: UUID,
        discordId: String = "discord-id",
        userName: String? = "discord-username",
        globalName: String? = "discord-global-name",
        joinedAt: Instant = Instant.now(),
        entityModifier: (StudentDiscord) -> Unit = {},
    ): StudentDiscord = studentDiscordRepository.save(
        StudentDiscord.newLinkedDiscordAccount(
            id = id,
            studentId = studentId,
            discordId = discordId,
            userName = userName,
            globalName = globalName,
            joinedAt = joinedAt,
        ).also {
            entityModifier(it)
        },
    )

    fun getTrader(
        id: UUID = UUIDv7.randomUUID(),
        profilePictureFileId: UUID? = null,
        listingOrder: Int = 1,
        firstName: String = "Joe",
        lastName: String = "Doe",
        phone: String? = null,
        biography: String? = null,
        country: Country = Country.CZ,
        tags: List<TraderTag> = listOf(TraderTag.CRYPTO, TraderTag.ETF, TraderTag.LIFESTYLE, TraderTag.MINDSET),
        badgeColor: BadgeColor = BadgeColor.GREEN_GRADIENT,
        commentControl: Boolean = false,
        socialLinkInstagram: String? = null,
        socialLinkLinkedin: String? = null,
        socialLinkFacebook: String? = null,
        socialLinkTwitter: String? = null,
        calendlyUrl: String? = null,
        calendlyUserUri: CalendlyUserUri? = null,
        checkoutVideoUrl: String? = null,
        position: String = "Certifikovaný Trader",
        mentoringAvailability: TraderMentoringAvailability = TraderMentoringAvailability.AUTOMATIC,
        networkingVisibility: NetworkingVisibility = NetworkingVisibility.ENABLED,
        locationId: UUID? = null,
        entityModifier: (Trader) -> Unit = {},
    ): Trader = traderRepository.saveAndFlush(
        Trader.promoteAccount(
            id = id,
            profilePictureFileId = profilePictureFileId,
            listingOrder = listingOrder,
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            biography = biography,
            country = country,
            tags = tags,
            badgeColor = badgeColor,
            commentControl = commentControl,
            socialLinkInstagram = socialLinkInstagram,
            socialLinkLinkedin = socialLinkLinkedin,
            socialLinkFacebook = socialLinkFacebook,
            socialLinkTwitter = socialLinkTwitter,
            calendlyUrl = calendlyUrl,
            calendlyUserUri = calendlyUserUri,
            checkoutVideoUrl = checkoutVideoUrl,
            position = position,
            networkingVisibility = networkingVisibility,
            locationId = locationId,
        ).also {
            it.patchMentoringAvailability(mentoringAvailability)
            entityModifier(it)
        },
    )

    fun getUserLocation(
        id: UUID = UUIDv7.randomUUID(),
        street: String? = "Česká 1",
        city: String? = "Brno",
        postalCode: String? = "60200",
        state: String? = "Česko",
        latitude: Double = 49.1950610,
        longitude: Double = 16.606836,
        obfuscatedLatitude: Double = 49.190600,
        obfuscatedLongitude: Double = 16.606840,
        entityModifier: (UserLocation) -> Unit = {},
    ): UserLocation = userLocationRepository.saveAndFlush(
        UserLocation.newLocation(
            id = id,
            street = street,
            city = city,
            postalCode = postalCode,
            state = state,
            latitude = latitude,
            longitude = longitude,
            obfuscatedLatitude = obfuscatedLatitude,
            obfuscatedLongitude = obfuscatedLongitude,
        ).also {
            entityModifier(it)
        },
    )

    fun getProduct(
        id: UUID = UUIDv7.randomUUID(),
        traderId: UUID,
        stripeIdentifier: StripeProductId = "prod_${id.suffix}",
        name: String = "5x Mentoring 1-on-1",
        description: String = "Dope Mentoring",
        altDescription: String = "Product for mentoring",
        sessionsCount: Int = 5,
        validityInDays: Int? = null,
        entityModifier: (Product) -> Unit = {},
    ): Product = productRepository.saveAndFlush(
        Product.newProduct(
            id = id,
            name = name,
            stripeIdentifier = stripeIdentifier,
            description = description,
            altDescription = altDescription,
            sessionsCount = sessionsCount,
            traderId = traderId,
            validityInDays = validityInDays,
        ).also {
            entityModifier(it)
        },
    )

    fun getMeeting(
        id: UUID = UUIDv7.randomUUID(),
        name: String = "Meeting 1",
        color: Color = Color.BLUE,
        startAt: Instant = "2025-01-01T10:00:00Z".toInstant(),
        finishAt: Instant = "2025-01-01T12:00:00Z".toInstant(),
        description: String? = "Meeting description",
        invitedTiers: List<StudentTier> = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS, StudentTier.EXCLUSIVE),
        invitedDiscordUsers: Boolean = true,
        meetingUrl: String = "meeting-url",
        recordingUrl: String = "recording-url",
        traderIds: List<UUID> = listOf(),
        entityModifier: (Meeting) -> Unit = {},
    ): Meeting = meetingRepository.saveAndFlush(
        Meeting.newMeeting(
            id = id,
            name = name,
            color = color,
            startAt = startAt,
            finishAt = finishAt,
            description = description,
            invitedTiers = invitedTiers,
            invitedDiscordUsers = invitedDiscordUsers,
            meetingUrl = meetingUrl,
            recordingUrl = recordingUrl,
        ).also { entityModifier(it) },
    ).also { meeting ->
        traderIds.forEachIndexed { idx, traderId ->
            traderInMeetingRepository.save(
                TraderInMeeting.addTraderToMeeting(
                    displayOrder = idx + 1,
                    traderId = traderId,
                    meetingId = meeting.id,
                ),
            )
        }
    }

    fun getVerificationCode(
        id: UUID = UUIDv7.randomUUID(),
        appUserId: UUID,
        code: String = "1234",
        expiresAt: Instant = Instant.now().plus(30, ChronoUnit.MINUTES),
        entityModifier: (VerificationCode) -> Unit = {},
    ): VerificationCode = verificationCodeRepository.save(
        VerificationCode.newVerificationCode(
            id = id,
            appUserId = appUserId,
            code = code,
            expiresAt = expiresAt,
        ).also {
            entityModifier(it)
        },
    )

    fun getHighlight(
        id: UUID = UUIDv7.randomUUID(),
        listingOrder: Int = 1,
        title: String = "Highlight 1",
        description: String = "Cool Description",
        visibleToTiers: List<StudentTier> = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS),
        visibleToDiscordUsers: Boolean = false,
        linkUrl: String? = null,
        button: AppButton? = null,
        entityModifier: (Highlight) -> Unit = {},
    ): Highlight = highlightRepository.save(
        Highlight.newHighlight(
            id = id,
            listingOrder = listingOrder,
            title = title,
            description = description,
            visibleToTiers = visibleToTiers,
            visibleToDiscordUsers = visibleToDiscordUsers,
            linkUrl = linkUrl,
            button = button,
        ).also { highlight -> entityModifier(highlight) },
    )

    fun getCourse(
        id: UUID = UUIDv7.randomUUID(),
        listingOrder: Int = 1,
        traderId: UUID,
        title: String = "Course",
        courseCategory: CourseCategory = CourseCategory.TRADING_BASICS,
        visibleToTiers: List<StudentTier> = listOf(StudentTier.MASTERCLASS),
        visibleToDiscordUsers: Boolean = true,
        description: String = "Description",
        color: Color = Color.GREEN,
        thumbnailUrl: String = "thumbnailUrl",
        thumbnailAnimationUrl: String = "thumbnailAnimationUrl",
        trailerUrl: String = "trailerUrl",
        public: Boolean = true,
        homepage: Boolean = true,
        entityModifier: (Course) -> Unit = {},
    ): Course = courseRepository.save(
        Course.newCourse(
            id = id,
            listingOrder = listingOrder,
            title = title,
            courseCategory = courseCategory,
            visibleToTiers = visibleToTiers,
            visibleToDiscordUsers = visibleToDiscordUsers,
            description = description,
            traderId = traderId,
            color = color,
            thumbnailUrl = thumbnailUrl,
            thumbnailAnimationUrl = thumbnailAnimationUrl,
            trailerUrl = trailerUrl,
            public = public,
            homepage = homepage,
        ).also { course ->
            entityModifier(course)
        },
    )

    fun getCourseModule(
        id: UUID = UUIDv7.randomUUID(),
        courseId: UUID,
        listingOrder: Int = 1,
        title: String = "Course Module",
        description: String = "Description",
        shortDescription: String = "Short Description",
        rewardDescription: String? = null,
        rewardCouponCode: String? = null,
        rewardButton: AppButtonWithLink? = null,
        comingSoon: Boolean = false,
        entityModifier: (CourseModule) -> Unit = {},
    ): CourseModule = courseModuleRepository.save(
        CourseModule.addNewModuleToCourse(
            id = id,
            courseId = courseId,
            listingOrder = listingOrder,
            title = title,
            description = description,
            shortDescription = shortDescription,
            rewardDescription = rewardDescription,
            rewardCouponCode = rewardCouponCode,
            rewardButton = rewardButton,
            comingSoon = comingSoon,
        ).also { courseModule ->
            entityModifier(courseModule)
        },
    )

    fun getLesson(
        id: UUID = UUIDv7.randomUUID(),
        courseModuleId: UUID,
        listingOrder: Int = 1,
        title: String = "Lesson",
        videoUrl: String = "url",
        description: String? = null,
        durationInSeconds: Int = 600, // 10 minutes
        thumbnailUrl: String = "thumbnail-url",
        thumbnailAnimationUrl: String = "thumbnail-animation-url",
        attachments: List<LessonAttachmentValue> = emptyList(),
        entityModifier: (Lesson) -> Unit = {},
    ): Lesson = lessonRepository.save(
        Lesson.addNewLessonToModule(
            id = id,
            courseModuleId = courseModuleId,
            listingOrder = listingOrder,
            title = title,
            videoUrl = videoUrl,
            description = description,
            durationInSeconds = durationInSeconds,
            thumbnailUrl = thumbnailUrl,
            thumbnailAnimationUrl = thumbnailAnimationUrl,
        ).also { lesson -> entityModifier(lesson) },
    ).also { lesson ->
        attachments.forEachIndexed { idx, attachment ->
            lessonAttachmentRepository.save(
                LessonAttachment.addAttachmentToLesson(
                    lessonId = lesson.id,
                    displayOrder = idx + 1,
                    nameWithExtension = attachment.name,
                    type = attachment.type,
                ),
            )
        }
    }

    fun getLessonAttachment(
        id: UUID = UUIDv7.randomUUID(),
        lessonId: UUID,
        displayOrder: Int = 1,
        name: String = "Attachment",
        type: LessonAttachmentType = LessonAttachmentType.PDF,
        entityModifier: (LessonAttachment) -> Unit = {},
    ): LessonAttachment = lessonAttachmentRepository.save(
        LessonAttachment.addAttachmentToLesson(
            id = id,
            lessonId = lessonId,
            displayOrder = displayOrder,
            nameWithExtension = name,
            type = type,
        ).also { lessonAttachment -> entityModifier(lessonAttachment) },
    )

    fun getLessonProgress(
        id: UUID = UUIDv7.randomUUID(),
        userId: UUID,
        lessonId: UUID,
        seconds: Int = 420,
        entityModifier: (LessonProgress) -> Unit = {},
        updatedTimestamp: Instant? = null,
    ): LessonProgress = lessonProgressRepository.save(
        LessonProgress.newLessonProgress(
            id = id,
            userId = userId,
            lessonId = lessonId,
        ).also { lessonProgress ->
            lessonProgress.update(seconds)
            entityModifier(lessonProgress)
        },
    ).apply {
        updatedTimestamp?.let { timestamp -> timestampModifier.setUpdatedAt(LESSON_PROGRESS, this.id, timestamp) }
    }

    fun getLessonComment(
        id: UUID = UUIDv7.randomUUID(),
        appUserId: UUID,
        lessonId: UUID,
        threadId: UUID? = null,
        text: String = "Comment content",
        entityModifier: (Comment) -> Unit = {},
    ): Comment = commentRepository.save(
        Comment.createNewComment(
            id = id,
            appUserId = appUserId,
            lessonId = lessonId,
            threadId = threadId,
            text = text,
        ).also {
            entityModifier(it)
        },
    )

    fun getThreadCommentNotification(
        id: UUID = UUIDv7.randomUUID(),
        appUserId: UUID,
        threadId: UUID,
        lastNotifiedAt: Instant = Instant.now(),
    ): ThreadCommentNotification = threadCommentNotificationRepository.save(
        ThreadCommentNotification.createNew(
            id = id,
            appUserId = appUserId,
            threadId = threadId,
            lastNotifiedAt = lastNotifiedAt,
        ),
    )

    fun getCommentLike(
        id: UUID = UUIDv7.randomUUID(),
        commentId: UUID,
        appUserId: UUID,
    ): CommentLike = commentLikeRepository.save(
        CommentLike.likeComment(
            id = id,
            commentId = commentId,
            appUserId = appUserId,
        ),
    )

    fun getCourseModuleProgress(
        id: UUID = UUIDv7.randomUUID(),
        userId: UUID,
        courseModuleId: UUID,
        entityModifier: (CourseModuleProgress) -> Unit = {},
        finishedAt: Instant,
    ): CourseModuleProgress = courseModuleProgressRepository.save(
        CourseModuleProgress.newFinishedCourseModule(
            id = id,
            userId = userId,
            courseModuleId = courseModuleId,
            finishedAt = finishedAt,
        ).also { courseModuleProgress ->
            entityModifier(courseModuleProgress)
        },
    )

    fun getCourseProgress(
        id: UUID = UUIDv7.randomUUID(),
        userId: UUID,
        courseId: UUID,
        entityModifier: (CourseProgress) -> Unit = {},
        finishedAt: Instant,
    ): CourseProgress = courseProgressRepository.save(
        CourseProgress.newFinishedCourse(
            id = id,
            userId = userId,
            courseId = courseId,
            finishedAt = finishedAt,
        ).also { courseProgress ->
            entityModifier(courseProgress)
        },
    )

    fun getMentoring(
        id: UUID = UUIDv7.randomUUID(),
        sessionIdentifier: StripeSessionId = "cs_${id.suffix}",
        studentId: UUID,
        productId: UUID,
        productName: String = "product name",
        productAltDescription: String = "alt description",
        expiresAt: Instant? = null,
        sessionCount: Int = 5,
        useSessions: Int = 0,
        entityModifier: (Mentoring) -> Unit = {},
        createdTimestamp: Instant? = null,
    ): Mentoring {
        require(useSessions >= 0)
        require(sessionCount >= useSessions)

        return mentoringRepository.save(
            Mentoring.newPurchasedMentoring(
                id = id,
                sessionIdentifier = sessionIdentifier,
                studentId = studentId,
                productId = productId,
                productName = productName,
                productAltDescription = productAltDescription,
                expiresAt = expiresAt,
                sessionCount = sessionCount,
            ).also { mentoring ->
                entityModifier(mentoring)
                repeat(useSessions) { mentoring.useSession() }
            },
        ).apply {
            createdTimestamp?.let { timestamp -> timestampModifier.setCreatedAt(MENTORING, this.id, timestamp) }
        }
    }

    fun getReferral(
        id: UUID = UUIDv7.randomUUID(),
        listingOrder: Int = 1,
        title: String? = "Test Referral",
        description: String? = "Test Description",
        visibleToTiers: List<StudentTier> = listOf(StudentTier.BASECAMP, StudentTier.MASTERCLASS),
        visibleToDiscordUsers: Boolean = false,
        linkUrl: String? = "https://example.com",
        rewardCouponCode: String? = "TEST123",
        entityModifier: (Referral) -> Unit = {},
    ): Referral = referralRepository.save(
        Referral.newReferral(
            id = id,
            listingOrder = listingOrder,
            title = title,
            description = description,
            visibleToTiers = visibleToTiers,
            visibleToDiscordUsers = visibleToDiscordUsers,
            linkUrl = linkUrl,
            rewardCouponCode = rewardCouponCode,
        ).also { referral ->
            entityModifier(referral)
        },
    )

    fun getMentoringMeeting(
        id: UUID = UUIDv7.randomUUID(),
        mentoringId: UUID,
        calendlyEventUri: CalendlyEventUri = "event-uri-${id.suffix}",
        color: Color = Color.GREEN,
        startAt: Instant = "2025-01-01T10:00:00Z".toInstant(),
        finishAt: Instant = "2025-01-01T12:00:00Z".toInstant(),
        meetingUrl: String? = "meeting-url",
        entityModifier: (MentoringMeeting) -> Unit = {},
    ): MentoringMeeting = mentoringMeetingRepository.save(
        MentoringMeeting.newMentoringMeeting(
            id = id,
            mentoringId = mentoringId,
            calendlyEventUri = calendlyEventUri,
            color = color,
            startAt = startAt,
            finishAt = finishAt,
            meetingUrl = meetingUrl,
        ).also { referral ->
            entityModifier(referral)
        },
    )

    fun getNetworkingMessage(
        id: UUID = UUIDv7.randomUUID(),
        senderUserId: UUID,
        recipientUserId: UUID,
        text: String = "Test networking message",
        entityModifier: (NetworkingMessage) -> Unit = {},
    ): NetworkingMessage = networkingMessageRepository.save(
        NetworkingMessage.create(
            id = id,
            senderUserId = senderUserId,
            recipientUserId = recipientUserId,
            text = text,
        ).also {
            entityModifier(it)
        },
    )

    fun getGameLevelReward(
        id: UUID = UUIDv7.randomUUID(),
        name: String = "Level Reward",
        gameLevel: GameLevel = GameLevel.ONE,
        type: GameLevelRewardType = GameLevelRewardType.ONLINE,
        description: String? = "Test Description",
        rewardCouponCode: String? = "TEST123",
        rewardButton: AppButtonWithLink? = null,
        listingOrder: Int = 0,
        entityModifier: (GameLevelReward) -> Unit = {},
    ): GameLevelReward = gameLevelRewardRepository.save(
        GameLevelReward.newGameLevelReward(
            id = id,
            name = name,
            gameLevel = gameLevel,
            type = type,
            description = description,
            rewardCouponCode = rewardCouponCode,
            rewardButton = rewardButton,
            listingOrder = listingOrder,
        ).also { levelReward ->
            entityModifier(levelReward)
        },
    )

    fun getGameDocument(
        id: UUID = UUIDv7.randomUUID(),
        studentId: UUID,
        type: GameDocumentType = GameDocumentType.PAYOUT,
        issuingCompany: IssuingCompany = IssuingCompany.FOR_TRADERS,
        payoutAmount: BigDecimal? = 100.toBigDecimal(),
        reachedLevel: GameLevel = GameLevel.ONE,
        payoutDate: LocalDate = LocalDate.now(),
        truthScore: Int = 100,
        scoreMessage: String? = null,
        entityModifier: (GameDocument) -> Unit = {},
    ): GameDocument = gameDocumentRepository.save(
        GameDocument.newGameDocument(
            id = id,
            studentId = studentId,
            type = type,
            issuingCompany = issuingCompany,
            payoutAmount = payoutAmount,
            reachedLevel = reachedLevel,
            payoutDate = payoutDate,
            truthScore = truthScore,
            scoreMessage = scoreMessage,
        ).also { gameDocument ->
            entityModifier(gameDocument)
        },
    )

    fun getGamePayoutSettings(
        id: UUID = UUIDv7.randomUUID(),
        year: Year = Year.of(2025),
        payoutGoal: BigDecimal = 1_000_000.toBigDecimal(),
        payoutOffset: BigDecimal = BigDecimal.ZERO,
        entityModifier: (GamePayoutSettings) -> Unit = {},
    ): GamePayoutSettings = gamePayoutSettingsRepository.saveAndFlush(
        GamePayoutSettings.newGamePayoutSettings(
            id = id,
            year = year,
            payoutGoal = payoutGoal,
            payoutOffset = payoutOffset,
        ).also { gamePayoutSettings ->
            entityModifier(gamePayoutSettings)
        },
    )
}
